import pkg_resources
import requests

# Get installed version
installed_version = pkg_resources.get_distribution("edge-tts").version
print(f"Installed edge-tts version: {installed_version}")

# Get latest version from PyPI
try:
    response = requests.get("https://pypi.org/pypi/edge-tts/json")
    latest_version = response.json()["info"]["version"]
    print(f"Latest edge-tts version: {latest_version}")
    
    if installed_version == latest_version:
        print("You have the latest version installed.")
    else:
        print(f"A newer version is available. Consider upgrading with: pip install --upgrade edge-tts")
except Exception as e:
    print(f"Could not check for latest version: {e}")