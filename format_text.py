#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Format the extracted Russian text with proper line breaks for TTS
"""

import re

def format_text_for_tts(input_file, output_file):
    """Format text with proper line breaks for TTS processing"""
    
    with open(input_file, 'r', encoding='utf-8') as f:
        text = f.read()
    
    # Split into sentences
    sentences = re.split(r'([.!?])\s+', text)
    
    formatted_lines = []
    current_line = ""
    
    for i in range(0, len(sentences), 2):
        if i + 1 < len(sentences):
            sentence = sentences[i] + sentences[i + 1]
        else:
            sentence = sentences[i]
        
        sentence = sentence.strip()
        if not sentence:
            continue
            
        # Check if this looks like a section header or date
        if re.match(r'^(ИЗ\s+ДНЕВНИКА|ИЗ\s+ПУТЕВЫХ\s+ЗАМЕТОК|ДОКТОРУ|АКАДЕМИКУ|\d{1,2}\s+[а-яё]+\s+\d{4}\s+г\.)', sentence):
            if current_line.strip():
                formatted_lines.append(current_line.strip())
                current_line = ""
            formatted_lines.append("")  # Empty line before header
            formatted_lines.append(sentence)
            formatted_lines.append("")  # Empty line after header
        else:
            # Add sentence to current line
            if current_line:
                current_line += " " + sentence
            else:
                current_line = sentence
                
            # If line gets too long, break it
            if len(current_line) > 200:
                formatted_lines.append(current_line)
                current_line = ""
    
    # Add any remaining text
    if current_line.strip():
        formatted_lines.append(current_line.strip())
    
    # Write formatted text
    with open(output_file, 'w', encoding='utf-8') as f:
        for line in formatted_lines:
            f.write(line + '\n')
    
    print(f"Formatted text saved to {output_file}")
    print(f"Total lines: {len(formatted_lines)}")

if __name__ == "__main__":
    format_text_for_tts("text2_russian_extracted.txt", "text2_russian_formatted.txt")
