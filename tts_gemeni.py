# https://ai.google.dev/gemini-api/docs/speech-generation
# https://ai.google.dev/gemini-api/docs/rate-limits
# <PERSON><PERSON><PERSON> gets text from file.txt using Google Gemeni saves result as mp3 audio file.
# For Gemini 2.5 Flash Preview TTS limits are:
# - Requests per minute (RPM) 10;
# - Tokens per minute (TPM) 10000 tokens (each token is 3.5 characters);
# - Requests per day (RPD) 15;
# <PERSON><PERSON><PERSON> splits text into chunks of 33682 characters or less.

from google import genai
from google.genai import types
from dotenv import load_dotenv
import os
import wave
import re
import time
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tts_gemeni_log.txt'),
        logging.StreamHandler()  # This will also print to console
    ]
)
logger = logging.getLogger(__name__)

# Limits
RPM = 10
TPM = 10000
RPD = 15
CHUNK_SIZE = 10000
TEXT = 'file.txt'

# Load variables from .env file
load_dotenv()
api_key=os.getenv("GOOGLE_API_KEY")
client = genai.Client(api_key=api_key)

# Set up the wave file to save the output:
def wave_file(filename, pcm, channels=1, rate=24000, sample_width=2) -> None:
   try:
      with wave.open(filename, "wb") as wf:
         wf.setnchannels(channels)
         wf.setsampwidth(sample_width)
         wf.setframerate(rate)
         wf.writeframes(pcm)
   except Exception as e:
      logger.error(f"Error writing to {filename}: {e}")

# Read text from the file
def read_text_from_file(text_file) -> str:
    try:
        with open(text_file, 'r', encoding='utf-8') as file:
            logger.info(f'{text_file} successfully opened.')
            return file.read()
    except Exception as e:
        logger.error(f"Error opening '{text_file}': {e}")
        
# Caclulate tokens
def calculate_tokens(chunk) -> int:
    try:
        prompt = [chunk]
        total_tokens = client.models.count_tokens(
            model="gemini-2.0-flash", contents=prompt
        )
        return total_tokens.total_tokens
    except Exception as e:
        logger.error(f"Error calculating tokens: {e}")
        return 0

# Function to split text into chunks with 33682 characters or less using sentence boundaries
def split_text_into_chunks(text, max_chars=CHUNK_SIZE) -> list[str]:
    try:    
        # Split text into sentences
        sentences = re.split(r'(?<=[.!?])\s+', text)
        chunks = []
        current_chunk = []
        current_char_count = 0

        for sentence in sentences:
            # Count characters in this sentence
            sentence_chars = len(sentence)
            
            # If adding this sentence would exceed the character limit
            if current_char_count + sentence_chars > max_chars:
                # Save current chunk if not empty
                if current_chunk:
                    chunks.append(" ".join(current_chunk))
                # Start new chunk with this sentence
                current_chunk = [sentence]
                current_char_count = sentence_chars
            else:
                # Add sentence to current chunk
                current_chunk.append(sentence)
                current_char_count += sentence_chars
        
        # Add the last chunk if not empty
        if current_chunk:
            chunks.append(" ".join(current_chunk))
        
        return chunks
    except Exception as e:
        logger.error(f"Error splitting text into chunks: {e}")

def generate_audio(text, filename) -> None:
    logger.info(f"Generating audio for {filename}...")
    start_time = time.time()
    try:
        response = client.models.generate_content(
          model="gemini-2.5-flash-preview-tts",
          contents=f'Read this in Russian calmly: {text}',
          config=types.GenerateContentConfig(
              response_modalities=["AUDIO"],
              speech_config=types.SpeechConfig(
                voice_config=types.VoiceConfig(
                    prebuilt_voice_config=types.PrebuiltVoiceConfig(
                      voice_name='Algieba',
                    )
                )
            )
          ),
      )
    except Exception as e:
        logger.error(f"Error generating audio: {e}")
    try:
        data = response.candidates[0].content.parts[0].inline_data.data
        wave_file(filename, data)
        elapsed_time = time.time() - start_time
        minutes = elapsed_time // 60
        sec = elapsed_time % 60
        logger.info(f"Audio saved to {filename}")
        logger.info(f"Spent time: {minutes:.0f} min {sec:.2f} sec")
    except Exception as e:
        logger.error(f"Error writing audio to file: {e}")

def main() -> None:
    text = read_text_from_file(TEXT)
    chunks = split_text_into_chunks(text)

    for i in range(0, len(chunks), 1):
        logger.info(f"Chunk {i+1}: {len(chunks[i])} characters, {calculate_tokens(chunks[i])} tokens")
        logger.info(chunks[i][0:50]+"...")
        logger.info(chunks[i][-50:]+'\n')
        filename = f"output_{i+1}.wav"
        generate_audio(chunks[i], filename)

if __name__ == "__main__":
    main()
    logger.info("Completed!")